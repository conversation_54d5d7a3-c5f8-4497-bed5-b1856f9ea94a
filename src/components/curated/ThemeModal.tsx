import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Palette, Save } from 'lucide-react'
import { CreateThemeRequest, UpdateThemeRequest, Theme } from '../../services/curatedService'

// Background colors for theme selection
const BACKGROUND_COLORS = [
  '#E8E5FF', '#DDD6FE', '#C4B5FD', '#A78BFA',
  '#DBEAFE', '#BFDBFE', '#93C5FD', '#60A5FA',
  '#CFFAFE', '#A5F3FC', '#67E8F9', '#22D3EE',
  '#8B5CF6', '#3B82F6', '#06B6D4', '#0891B2',
  '#D1FAE5', '#A7F3D0', '#6EE7B7', '#34D399',
  '#FEF3C7', '#FDE68A', '#FCD34D', '#F59E0B',
  '#FED7AA', '#FDBA74', '#FB923C', '#F97316',
  '#10B981', '#D97706', '#EA580C', '#C2410C',
  '#FEE2E2', '#FECACA', '#F87171', '#EF4444',
  '#F3F4F6', '#E5E7EB', '#D1D5DB', '#9CA3AF',
  '#FDF2F8', '#FCE7F3', '#F9A8D4', '#EC4899',
  '#DC2626', '#6B7280', '#BE185D', '#4B5563'
]

// Font colors for theme selection
const FONT_COLORS = [
  '#6B46C1', '#7C3AED', '#8B5CF6', '#9333EA',
  '#2563EB', '#3B82F6', '#1D4ED8', '#1E40AF',
  '#0891B2', '#0E7490', '#155E75', '#164E63',
  '#FFFFFF', '#F8FAFC', '#F1F5F9', '#E2E8F0',
  '#059669', '#10B981', '#047857', '#065F46',
  '#D97706', '#F59E0B', '#B45309', '#92400E',
  '#EA580C', '#F97316', '#C2410C', '#9A3412',
  '#DC2626', '#EF4444', '#B91C1C', '#991B1B',
  '#374151', '#4B5563', '#6B7280', '#9CA3AF',
  '#BE185D', '#DB2777', '#A21CAF', '#86198F',
  '#000000', '#1F2937', '#111827', '#030712'
]

// Icon options for theme selection
const ICON_OPTIONS = [
  '🎨', '🎭', '🎪', '🎯', '🎲', '🎸', '🎺', '🎻', '🎹', '🎤',
  '📚', '📖', '📝', '📊', '📈', '📉', '📋', '📌', '📍', '📎',
  '🌟', '⭐', '✨', '💫', '🌙', '☀️', '🌈', '🔥', '💎', '🎁',
  '🚀', '✈️', '🚗', '🚲', '⛵', '🏠', '🏢', '🏰', '🗼', '🎡',
  '💡', '🔬', '🔭', '⚗️', '🧪', '🔧', '⚙️', '🔨', '⚡', '🔋',
  '🎯', '🏆', '🥇', '🏅', '🎖️', '🏵️', '🎗️', '🎀', '🎊', '🎉',
  '🌺', '🌸', '🌼', '🌻', '🌷', '🌹', '🌿', '🍀', '🌱', '🌳',
  '🦋', '🐝', '🐞', '🦄', '🐰', '🐱', '🐶', '🐸', '🐧', '🦊'
]

// Color Picker Component
interface ColorPickerProps {
  selectedColor: string
  onColorSelect: (color: string) => void
  label: string
  colors: string[]
  type: 'background' | 'text'
  error?: string
}

const ColorPicker: React.FC<ColorPickerProps> = ({
  selectedColor,
  onColorSelect,
  label,
  colors,
  type,
  error
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const gridSize = 5 // Fixed 5x5 grid

  return (
    <div className="space-y-3">
      <label className="block text-sm font-medium text-foreground">{label}</label>

      {/* Selected Color Display - Clickable */}
      <button
        type="button"
        onClick={() => setIsOpen(true)}
        className={`w-full h-12 rounded-lg border-2 flex items-center justify-center transition-all hover:border-primary cursor-pointer ${
          error ? 'border-red-500' : 'border-border'
        }`}
        style={{ backgroundColor: selectedColor }}
      >
        {type === 'text' ? (
          <span className="text-sm font-bold" style={{ color: selectedColor }}>
            Aa
          </span>
        ) : (
          <div className="w-8 h-8 rounded-full border-2 border-white/50"
               style={{ backgroundColor: selectedColor }} />
        )}
      </button>

      {/* Color Picker Modal */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setIsOpen(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="bg-background border border-border rounded-xl shadow-2xl max-w-md w-full max-h-[80vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Modal Header */}
              <div className="p-6 border-b border-border">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-foreground">
                    Choose {type === 'background' ? 'Background' : 'Text'} Color
                  </h3>
                  <button
                    type="button"
                    onClick={() => setIsOpen(false)}
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>


              </div>

              {/* Color Grid */}
              <div className="p-6">
                {/* Debug info */}
                {/* <div className="mb-2 text-xs text-muted-foreground">
                  Current grid: {gridSize}×{gridSize}
                </div> */}

                <div
                  className="max-h-96 overflow-y-auto"
                  style={{
                    scrollbarWidth: 'thin',
                    scrollbarColor: '#9CA3AF #F3F4F6'
                  }}
                >
                  <div
                    className={`grid gap-3 ${
                      gridSize === 3 ? 'grid-cols-3' :
                      gridSize === 4 ? 'grid-cols-4' :
                      gridSize === 5 ? 'grid-cols-5' :
                      'grid-cols-4'
                    }`}
                    style={{
                      display: 'grid',
                      gridTemplateColumns: gridSize === 3 ? 'repeat(3, 1fr)' :
                                          gridSize === 4 ? 'repeat(4, 1fr)' :
                                          gridSize === 5 ? 'repeat(5, 1fr)' :
                                          'repeat(4, 1fr)'
                    }}
                  >
                    {colors.map((color, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()
                          onColorSelect(color)
                          setIsOpen(false)
                        }}
                        className={`relative rounded-lg border-2 transition-all hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary/50 ${
                          gridSize === 3 ? 'w-16 h-16' :
                          gridSize === 4 ? 'w-12 h-12' :
                          gridSize === 5 ? 'w-10 h-10' :
                          'w-12 h-12'
                        } ${
                          selectedColor === color
                            ? 'border-primary ring-2 ring-primary/20 shadow-lg bg-primary/10'
                            : 'border-border hover:border-primary/50'
                        }`}
                        style={{ backgroundColor: color }}
                        title={color}
                      >
                        {type === 'text' && (
                          <span className={`font-bold ${
                            gridSize === 3 ? 'text-lg' :
                            gridSize === 4 ? 'text-sm' :
                            gridSize === 5 ? 'text-xs' :
                            'text-sm'
                          }`} style={{ color: color }}>
                            A
                          </span>
                        )}
                        {selectedColor === color && (
                          <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full flex items-center justify-center">
                            <div className="w-1 h-1 bg-white rounded-full" />
                          </div>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Modal Footer */}
              <div className="p-6 border-t border-border bg-muted/20">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div
                      className="w-10 h-10 rounded-lg border-2 border-primary/30 bg-primary/5 flex items-center justify-center"
                      style={{ backgroundColor: selectedColor }}
                    >
                      {type === 'text' && (
                        <span className="text-lg font-bold" style={{ color: selectedColor }}>
                          A
                        </span>
                      )}
                    </div>
                    <div className="flex flex-col">
                      <span className="text-sm font-medium text-foreground">
                        Selected {type === 'background' ? 'Background' : 'Text'} Color
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {selectedColor || 'None selected'}
                      </span>
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      setIsOpen(false)
                    }}
                    className="px-6 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors font-medium"
                  >
                    Done
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
    </div>
  )
}

// Icon Picker Component
interface IconPickerProps {
  selectedIcon: string
  onIconSelect: (icon: string) => void
  error?: string
}

const IconPicker: React.FC<IconPickerProps> = ({
  selectedIcon,
  onIconSelect,
  error
}) => {
  const [isOpen, setIsOpen] = useState(false)



  return (
    <div className="space-y-3">
      <label className="block text-sm font-medium text-foreground">Icon</label>

      {/* Selected Icon Display - Clickable */}
      <button
        type="button"
        onClick={() => setIsOpen(true)}
        className={`w-full h-12 rounded-lg border-2 flex items-center justify-center transition-all hover:border-primary cursor-pointer ${
          error ? 'border-red-500' : 'border-border'
        }`}
      >
        <div className="text-2xl">{selectedIcon}</div>
      </button>

      {/* Icon Picker Modal */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setIsOpen(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="bg-background border border-border rounded-xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Modal Header */}
              <div className="p-6 border-b border-border">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-foreground">
                    Choose Icon
                  </h3>
                  <button
                    type="button"
                    onClick={() => setIsOpen(false)}
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>


              </div>

              {/* Icon Grid */}
              <div className="p-6">


                <div
                  className="max-h-96 overflow-y-auto"
                  style={{
                    scrollbarWidth: 'thin',
                    scrollbarColor: '#9CA3AF #F3F4F6'
                  }}
                >
                  <div
                    className="grid grid-cols-5 gap-3"
                    style={{
                      display: 'grid',
                      gridTemplateColumns: 'repeat(5, 1fr)',
                      width: '100%'
                    }}
                  >
                    {ICON_OPTIONS.map((icon, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()
                          onIconSelect(icon)
                          setIsOpen(false)
                        }}
                        className={`relative w-10 h-10 rounded-lg border-2 transition-all hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary/50 flex items-center justify-center ${
                          selectedIcon === icon
                            ? 'border-primary ring-2 ring-primary/20 shadow-lg bg-primary/10'
                            : 'border-border hover:border-primary/50 hover:bg-primary/5'
                        }`}
                        title={icon}
                      >
                        <span className="text-base">
                          {icon}
                        </span>
                        {selectedIcon === icon && (
                          <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full flex items-center justify-center">
                            <div className="w-1 h-1 bg-white rounded-full" />
                          </div>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Modal Footer */}
              <div className="p-6 border-t border-border bg-muted/20">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-lg border-2 border-primary/30 bg-primary/5 flex items-center justify-center">
                      <span className="text-xl">{selectedIcon || '🎨'}</span>
                    </div>
                    <div className="flex flex-col">
                      <span className="text-sm font-medium text-foreground">
                        Selected Icon
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {selectedIcon || 'None selected'}
                      </span>
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      setIsOpen(false)
                    }}
                    className="px-6 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors font-medium"
                  >
                    Done
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
    </div>
  )
}



interface ThemeModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (themeData: CreateThemeRequest | UpdateThemeRequest) => Promise<void>
  theme?: Theme | null
  loading?: boolean
}

const ThemeModal: React.FC<ThemeModalProps> = ({
  isOpen,
  onClose,
  onSave,
  theme,
  loading = false
}) => {
  const [formData, setFormData] = useState<CreateThemeRequest>({
    name: '',
    name_en: '',
    description: '',
    description_en: '',
    category: '',
    icon: '🎨',
    background_color: BACKGROUND_COLORS[0],
    font_color: FONT_COLORS[0],
    is_active: true
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [showSuccess, setShowSuccess] = useState(false)
  const [successMessage, setSuccessMessage] = useState('')

  // Reset form when modal opens/closes or theme changes
  useEffect(() => {
    if (isOpen) {
      if (theme) {
        setFormData({
          name: theme.name || '',
          name_en: theme.name_en || '',
          description: theme.description || '',
          description_en: theme.description_en || '',
          category: theme.category || '',
          icon: theme.icon || '🎨',
          background_color: theme.background_color || theme.color || BACKGROUND_COLORS[0],
          font_color: theme.font_color || FONT_COLORS[0],
          is_active: theme.is_active ?? true
        })
      } else {
        setFormData({
          name: '',
          name_en: '',
          description: '',
          description_en: '',
          category: '',
          icon: '🎨',
          background_color: BACKGROUND_COLORS[0],
          font_color: FONT_COLORS[0],
          is_active: true
        })
      }
      setErrors({})
    }
  }, [isOpen, theme])

  const handleInputChange = (field: keyof CreateThemeRequest, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}
    if (!formData.name.trim()) newErrors.name = 'Name is required'
    if (!formData.name_en.trim()) newErrors.name_en = 'English name is required'
    if (!formData.description.trim()) newErrors.description = 'Description is required'
    if (!formData.description_en.trim()) newErrors.description_en = 'English description is required'
    if (!formData.category.trim()) newErrors.category = 'Category is required'
    if (!formData.icon.trim()) newErrors.icon = 'Icon is required'
    if (!formData.background_color.trim()) newErrors.background_color = 'Background color is required'
    if (!formData.font_color.trim()) newErrors.font_color = 'Font color is required'
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateForm()) return
    try {
      await onSave(formData)
      const message = theme
        ? `Successfully updated theme "${formData.name_en}"`
        : `Successfully created theme "${formData.name_en}"`
      setSuccessMessage(message)
      setShowSuccess(true)

      // Hide success message and close modal after 2 seconds
      setTimeout(() => {
        setShowSuccess(false)
        onClose()
      }, 2000)
    } catch (error) {
      console.error('Error saving theme:', error)
    }
  }

  const commonInputClasses = "w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={onClose}
        />
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="relative w-full max-w-6xl mx-4 bg-background border border-border rounded-xl shadow-xl max-h-[95vh] overflow-hidden"
        >
          <div className="flex items-center justify-between p-6 border-b border-border">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                <Palette className="w-4 h-4 text-white" />
              </div>
              <h2 className="text-xl font-semibold text-foreground">
                {theme ? 'Edit Theme' : 'Create New Theme'}
              </h2>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-muted-foreground hover:text-foreground transition-colors rounded-lg hover:bg-accent"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
          <div className="flex-1">
            <div className="p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-foreground">Theme Colors</h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <ColorPicker
                          selectedColor={formData.background_color}
                          onColorSelect={(color) => handleInputChange('background_color', color)}
                          label="Background Color"
                          colors={BACKGROUND_COLORS}
                          type="background"
                          error={errors.background_color}
                        />
                        <ColorPicker
                          selectedColor={formData.font_color}
                          onColorSelect={(color) => handleInputChange('font_color', color)}
                          label="Text Color"
                          colors={FONT_COLORS}
                          type="text"
                          error={errors.font_color}
                        />
                      </div>
                    </div>
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-foreground">Theme Information</h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-foreground mb-2">Name</label>
                          <input
                            type="text"
                            value={formData.name}
                            onChange={(e) => handleInputChange('name', e.target.value)}
                            className={`${commonInputClasses} ${errors.name ? 'border-red-500' : ''}`}
                            placeholder="Theme name"
                          />
                          {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-foreground mb-2">English Name</label>
                          <input
                            type="text"
                            value={formData.name_en}
                            onChange={(e) => handleInputChange('name_en', e.target.value)}
                            className={`${commonInputClasses} ${errors.name_en ? 'border-red-500' : ''}`}
                            placeholder="English theme name"
                          />
                          {errors.name_en && <p className="text-red-500 text-xs mt-1">{errors.name_en}</p>}
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-foreground mb-2">Category</label>
                          <input
                            type="text"
                            value={formData.category}
                            onChange={(e) => handleInputChange('category', e.target.value)}
                            className={`${commonInputClasses} ${errors.category ? 'border-red-500' : ''}`}
                            placeholder="Theme category"
                          />
                          {errors.category && <p className="text-red-500 text-xs mt-1">{errors.category}</p>}
                        </div>
                        <div>
                          <IconPicker
                            selectedIcon={formData.icon}
                            onIconSelect={(icon) => handleInputChange('icon', icon)}
                            error={errors.icon}
                          />
                        </div>
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-foreground mb-2">Description</label>
                          <textarea
                            value={formData.description}
                            onChange={(e) => handleInputChange('description', e.target.value)}
                            className={`${commonInputClasses} ${errors.description ? 'border-red-500' : ''}`}
                            placeholder="Theme description"
                            rows={3}
                          />
                          {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-foreground mb-2">English Description</label>
                          <textarea
                            value={formData.description_en}
                            onChange={(e) => handleInputChange('description_en', e.target.value)}
                            className={`${commonInputClasses} ${errors.description_en ? 'border-red-500' : ''}`}
                            placeholder="English theme description"
                            rows={3}
                          />
                          {errors.description_en && <p className="text-red-500 text-xs mt-1">{errors.description_en}</p>}
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <input
                          type="checkbox"
                          id="is_active"
                          checked={formData.is_active}
                          onChange={(e) => handleInputChange('is_active', e.target.checked)}
                          className="w-4 h-4 text-primary border-border rounded focus:ring-primary/20"
                        />
                        <label htmlFor="is_active" className="text-sm font-medium text-foreground">
                          Active Theme
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Right Column - Dynamic Preview */}
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-foreground">Text examples with your selected colors:</h3>

                      {/* Show examples only when both colors are selected */}
                      {formData.background_color && formData.font_color ? (
                        <div className="grid grid-cols-1 gap-4">
                        {/* Theme Card Example */}
                        <div
                          className="p-6 rounded-lg text-center border-2 transition-all duration-300"
                          style={{
                            backgroundColor: formData.background_color,
                            color: formData.font_color,
                            borderColor: formData.font_color + '20'
                          }}
                        >
                          <div className="text-3xl mb-3">{formData.icon || '🎨'}</div>
                          <div className="font-semibold text-lg mb-1">
                            {formData.name || formData.name_en || 'Theme Name'}
                          </div>
                          <div className="text-sm opacity-80 mb-2">
                            {formData.category || 'Category'}
                          </div>
                          <div className="text-xs opacity-60">
                            {formData.description || formData.description_en || 'Theme description...'}
                          </div>
                        </div>

                        {/* Content Card Example */}
                        <div
                          className="p-4 rounded-lg border transition-all duration-300"
                          style={{
                            backgroundColor: formData.background_color,
                            color: formData.font_color,
                            borderColor: formData.font_color + '30'
                          }}
                        >
                          <div className="flex items-center gap-3 mb-3">
                            <div className="text-xl">{formData.icon || '🎨'}</div>
                            <div>
                              <div className="font-medium text-sm">
                                {formData.name || formData.name_en || 'Theme Name'}
                              </div>
                              <div className="text-xs opacity-75">
                                {formData.category || 'Category'}
                              </div>
                            </div>
                          </div>
                          <div className="text-xs opacity-90">
                            {formData.description || formData.description_en || 'This is how content will appear with your theme colors. The text should be easily readable with good contrast.'}
                          </div>
                        </div>

                        {/* Button & Badge Examples */}
                        <div className="grid grid-cols-2 gap-3">
                          <div className="p-3 bg-muted/10 rounded-lg text-center">
                            <div className="text-xs text-muted-foreground mb-2">Button Style</div>
                            <button
                              className="px-4 py-2 rounded-lg font-medium text-sm transition-all duration-300"
                              style={{
                                backgroundColor: formData.background_color,
                                color: formData.font_color
                              }}
                            >
                              {formData.name || formData.name_en || 'Click Me'}
                            </button>
                          </div>

                          <div className="p-3 bg-muted/10 rounded-lg text-center">
                            <div className="text-xs text-muted-foreground mb-2">Badge Style</div>
                            <div
                              className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition-all duration-300"
                              style={{
                                backgroundColor: formData.background_color,
                                color: formData.font_color
                              }}
                            >
                              {formData.icon || '🎨'} {formData.category || 'Tag'}
                            </div>
                          </div>
                        </div>
                      </div>
                      ) : (
                        <div className="text-center py-8 text-muted-foreground">
                          <div className="text-4xl mb-3">🎨</div>
                          <p className="text-sm">Select background and text colors to see examples</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>

          {/* Success Message */}
          <AnimatePresence>
            {showSuccess && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="absolute top-4 left-1/2 transform -translate-x-1/2 z-50"
              >
                <div className="bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-2">
                  <div className="w-5 h-5 bg-white rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                  </div>
                  <span className="font-medium">{successMessage}</span>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Footer */}
          <div className="flex items-center justify-end gap-3 p-6 border-t border-border">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              onClick={handleSubmit}
              disabled={loading || showSuccess}
              className="flex items-center gap-2 px-6 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  Saving...
                </>
              ) : showSuccess ? (
                <>
                  <div className="w-4 h-4 bg-white rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-primary rounded-full" />
                  </div>
                  Success!
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  {theme ? 'Update Theme' : 'Create Theme'}
                </>
              )}
            </button>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  )
}

export default ThemeModal