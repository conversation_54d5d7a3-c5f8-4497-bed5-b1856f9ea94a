import React from 'react'
import { motion } from 'framer-motion'
import { ArrowRight, BookOpen, FileText, Edit, Trash2, MoreVertical } from 'lucide-react'
import { Theme } from '../../services/curatedService'
import { cn } from '../../utils/cn'

export interface ThemeCardProps {
  theme: Theme
  onClick?: (theme: Theme) => void
  className?: string
  showStats?: boolean
  onEdit?: (theme: Theme) => void
  onDelete?: (themeId: string) => void
  onViewTasks?: (theme: Theme) => void
  showActions?: boolean
}

/**
 * ThemeCard Component
 * Displays a theme with icon, colors, and statistics
 * Used in Atelier page for theme discovery
 */
const ThemeCard: React.FC<ThemeCardProps> = React.memo(({
  theme,
  onClick,
  className,
  showStats = true,
  onEdit,
  onDelete,
  onViewTasks,
  showActions = false
}) => {
  // Card is no longer clickable for navigation
  const handleClick = () => {
    // Only call onClick if provided (for details view, not navigation)
    onClick?.(theme)
  }

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation()
    onEdit?.(theme)
  }

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation()
    // Use _id or id as fallback
    const themeId = theme._id || theme.id
    if (themeId) {
      onDelete?.(themeId)
    }
  }

  const handleViewTasks = (e: React.MouseEvent) => {
    e.stopPropagation()
    onViewTasks?.(theme)
  }

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      culture: 'from-purple-500 to-pink-500',
      geography: 'from-green-500 to-teal-500',
      history: 'from-amber-500 to-orange-500',
      language: 'from-blue-500 to-indigo-500',
      literature: 'from-rose-500 to-red-500',
      science: 'from-cyan-500 to-blue-500',
      sports: 'from-emerald-500 to-green-500',
      politics: 'from-gray-500 to-slate-500',
      economy: 'from-yellow-500 to-amber-500',
      religion: 'from-violet-500 to-purple-500',
      art: 'from-pink-500 to-rose-500',
      music: 'from-indigo-500 to-blue-500',
      dance: 'from-fuchsia-500 to-pink-500',
      food: 'from-orange-500 to-red-500',
      festivals: 'from-yellow-500 to-orange-500',
      traditions: 'from-purple-500 to-violet-500',
      customs: 'from-teal-500 to-cyan-500',
      wildlife: 'from-green-500 to-emerald-500',
      nature: 'from-lime-500 to-green-500',
      tourism: 'from-sky-500 to-blue-500',
      education: 'from-blue-500 to-purple-500',
      technology: 'from-gray-500 to-blue-500',
      health: 'from-red-500 to-pink-500',
      agriculture: 'from-green-500 to-yellow-500',
      business: 'from-blue-500 to-gray-500',
      entertainment: 'from-purple-500 to-pink-500'
    }
    return colors[category] || 'from-gray-500 to-slate-500'
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      whileHover={{ scale: 1.05, y: -8 }}
      transition={{ duration: 0.2 }}
      className={cn(
        'relative bg-card border border-border rounded-xl p-6 cursor-pointer',
        'hover:shadow-xl hover:border-primary/30 transition-all duration-300',
        'group overflow-hidden',
        className
      )}
      onClick={handleClick}
    >
      {/* Background gradient */}
      <div 
        className={cn(
          'absolute inset-0 bg-gradient-to-br opacity-5 group-hover:opacity-10 transition-opacity duration-300',
          getCategoryColor(theme.category)
        )}
      />

      {/* Theme icon and color indicator */}
      <div className="relative z-10 flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div
            className="w-12 h-12 rounded-lg flex items-center justify-center text-2xl shadow-sm"
            style={{
              backgroundColor: theme.background_color || `${theme.color}20`,
              color: theme.font_color || theme.color
            }}
          >
            {theme.icon}
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors">
              {theme.name_en}
            </h3>
            <p className="text-sm text-muted-foreground capitalize">
              {theme.category}
            </p>
          </div>
        </div>

        {showActions ? (
          <div className="flex items-center gap-2">
            <button
              onClick={handleEdit}
              className="p-2 rounded-full hover:bg-accent text-muted-foreground hover:text-foreground transition-colors"
            >
              <Edit className="w-4 h-4" />
            </button>
            <button
              onClick={handleDelete}
              className="p-2 rounded-full hover:bg-red-100 dark:hover:bg-red-900/20 text-muted-foreground hover:text-red-600 dark:hover:text-red-400 transition-colors"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        ) : (
          <ArrowRight className="w-5 h-5 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all duration-200" />
        )}
      </div>

      {/* Theme description */}
      <div className="relative z-10 mb-4">
        <p className="text-sm text-muted-foreground line-clamp-2">
          {theme.description_en || theme.description}
        </p>
      </div>

      {/* View Tasks Button - Smaller and more attractive */}
      {onViewTasks && (
        <div className="relative z-10 mb-3">
          <button
            onClick={handleViewTasks}
            className="px-3 py-1.5 bg-primary/10 text-primary border border-primary/20 rounded-md hover:bg-primary hover:text-primary-foreground transition-all duration-200 text-xs font-medium flex items-center gap-1.5 ml-auto"
          >
            <span>View Tasks</span>
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      )}

      {/* Statistics */}
      {showStats && theme.statistics && (
        <div className="relative z-10 grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <BookOpen className="w-4 h-4 text-primary" />
              <span className="text-lg font-semibold text-foreground">
                {theme.statistics.total_content_sets}
              </span>
            </div>
            <p className="text-xs text-muted-foreground">Content Sets</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <FileText className="w-4 h-4 text-primary" />
              <span className="text-lg font-semibold text-foreground">
                {theme.statistics.total_content_items}
              </span>
            </div>
            <p className="text-xs text-muted-foreground">Total Items</p>
          </div>
        </div>
      )}

      {/* Active indicator */}
      {theme.is_active && (
        <div className="absolute top-3 right-3 w-2 h-2 bg-green-500 rounded-full" />
      )}

      {/* Hover effect overlay */}
      <div 
        className={cn(
          'absolute inset-0 bg-gradient-to-r opacity-0 group-hover:opacity-10 transition-opacity duration-300',
          getCategoryColor(theme.category)
        )}
      />
    </motion.div>
  )
})

ThemeCard.displayName = 'ThemeCard'

export default ThemeCard
