import React from 'react'
import { Outlet } from 'react-router-dom'
import SideNavigation from './SideNavigation'

/**
 * App Layout component with persistent sidebar navigation
 * This layout wraps all protected routes and prevents sidebar re-rendering
 */
const AppLayout: React.FC = React.memo(() => {
  return (
    <div className="flex h-screen bg-background">
      {/* Persistent Sidebar Navigation */}
      <SideNavigation />

      {/* Content Area - Renders the current route */}
      <Outlet />
    </div>
  )
})

AppLayout.displayName = 'AppLayout'

export default AppLayout
