import React, { useState, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { Palette, Grid, List, Filter, ChevronDown, X, <PERSON>rkles, ArrowUpDown, Plus, RefreshCw } from 'lucide-react'
import MainLayout from '../../../components/layout/MainLayout'
import {
  ThemeCard,
  ThemeModal,
  SearchBar,
  LoadingGrid,
  EmptyState
} from '../../../components/curated'
import { useThemes, useThemeFilterOptions } from '../hooks'
import { Theme, CuratedService, CreateThemeRequest, UpdateThemeRequest } from '../../../services/curatedService'
import { useAppSelector } from '../../../store/hooks'
import { cn } from '../../../utils/cn'

/**
 * Atelier Page
 * Theme discovery and selection gallery
 * Displays all available themes with statistics and filtering
 */
const Atelier: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAppSelector((state) => state.auth)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [selectedSortOrder, setSelectedSortOrder] = useState<'asc' | 'desc'>('asc')

  // Theme management state
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingTheme, setEditingTheme] = useState<Theme | null>(null)
  const [modalLoading, setModalLoading] = useState(false)

  const {
    themes,
    loading,
    error,
    pagination,
    filters,
    setFilters,
    refetch,
    clearFilters
  } = useThemes()

  // Get theme filter options
  const {
    filterOptions: themeFilterOptions
  } = useThemeFilterOptions()

  // Get categories from filter options or fallback to themes
  const categories = React.useMemo(() => {
    if (themeFilterOptions?.categories) {
      return themeFilterOptions.categories
    }
    // Fallback to extracting from themes
    const cats = themes.map((theme: any) => theme.category).filter((cat: any): cat is string => typeof cat === 'string')
    return Array.from(new Set(cats)).sort()
  }, [themeFilterOptions, themes])

  const handleSearch = useCallback((searchTerm: string) => {
    setFilters({ search: searchTerm })
  }, [setFilters])

  const handleCategoryFilter = useCallback((category: string) => {
    setSelectedCategory(category)
    setFilters({ category: category || undefined })
  }, [setFilters])

  const handleSortOrderChange = useCallback((sortOrder: 'asc' | 'desc') => {
    setSelectedSortOrder(sortOrder)
    setFilters({ sort_order: sortOrder })
  }, [setFilters])

  const handleThemeClick = useCallback((theme: Theme) => {
    // Theme card click no longer navigates - only for viewing details
    console.log('🎨 THEME CLICKED (no navigation):', theme.name_en)
  }, [])

  const handleViewTasks = useCallback((theme: Theme) => {
    // Navigate to Editors Pick page filtered by this theme
    console.log('🎨 VIEW TASKS CLICKED:')
    console.log('  Theme ID:', theme.id)
    console.log('  Theme Name:', theme.name_en)

    const themeId = theme._id || theme.id
    const url = `/editors-pick?theme_id=${themeId}`
    console.log('🎨 NAVIGATING TO:', url)

    navigate(url)
  }, [navigate])

  const handleClearFilters = useCallback(() => {
    setSelectedCategory('')
    setSelectedSortOrder('asc')
    clearFilters()
  }, [clearFilters])

  // Theme management functions
  const handleCreateTheme = useCallback(() => {
    setEditingTheme(null)
    setIsModalOpen(true)
  }, [])

  const handleEditTheme = useCallback((theme: Theme) => {
    setEditingTheme(theme)
    setIsModalOpen(true)
  }, [])

  const handleSaveTheme = useCallback(async (themeData: CreateThemeRequest | UpdateThemeRequest) => {
    try {
      setModalLoading(true)
      if (editingTheme) {
        // Update existing theme - use _id or id as fallback
        const themeId = editingTheme._id || editingTheme.id
        if (!themeId) {
          throw new Error('Theme ID is missing')
        }
        await CuratedService.updateTheme(themeId, themeData as UpdateThemeRequest)
      } else {
        // Create new theme
        await CuratedService.createTheme(themeData as CreateThemeRequest)
      }
      // Refresh themes list
      await refetch()
      setIsModalOpen(false)
    } catch (error) {
      console.error('Error saving theme:', error)
    } finally {
      setModalLoading(false)
    }
  }, [editingTheme, refetch])

  const handleDeleteTheme = useCallback(async (themeId: string) => {
    if (!confirm('Are you sure you want to delete this theme?')) return

    try {
      if (!themeId) {
        throw new Error('Theme ID is missing')
      }
      await CuratedService.deleteTheme(themeId)
      await refetch()
    } catch (error) {
      console.error('Error deleting theme:', error)
    }
  }, [refetch])

  const isAdmin = user?.role === 'admin' || user?.role === 'agent'
  const hasActiveFilters = !!(filters.search || filters.category || (filters.sort_order && filters.sort_order !== 'asc'))

  // Top content (search and filters)
  const topContent = (
    <div className="space-y-4">
      {/* Search and view controls */}
      <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
        <div className="flex-1 max-w-md">
          <SearchBar
            placeholder="Search themes..."
            value={filters.search || ''}
            onChange={handleSearch}
            className="w-full"
          />
        </div>

        <div className="flex items-center gap-4">
          {/* Active filters indicator and clear button */}
          {hasActiveFilters && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                {[filters.search, filters.category, filters.sort_order && filters.sort_order !== 'asc' ? filters.sort_order : null].filter(Boolean).length} filters active
              </span>
              <button
                onClick={handleClearFilters}
                className="text-sm text-primary hover:text-primary/80 transition-colors"
              >
                Clear all
              </button>
            </div>
          )}

          {/* View mode toggle */}
          <div className="flex items-center border border-border rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={cn(
                'p-2 rounded transition-all duration-200',
                viewMode === 'grid'
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:text-foreground'
              )}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={cn(
                'p-2 rounded transition-all duration-200',
                viewMode === 'list'
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:text-foreground'
              )}
            >
              <List className="w-4 h-4" />
            </button>
          </div>

          {/* Refresh button */}
          <button
            onClick={refetch}
            disabled={loading}
            className={cn(
              'p-2 rounded-lg border border-border',
              'text-muted-foreground hover:text-foreground',
              'hover:border-primary/50 transition-all duration-200',
              'focus:outline-none focus:ring-2 focus:ring-primary/20',
              'disabled:opacity-50 disabled:cursor-not-allowed'
            )}
          >
            <RefreshCw className={cn('w-4 h-4', loading && 'animate-spin')} />
          </button>

          {/* Add Theme button (admin only) */}
          {isAdmin && (
            <button
              onClick={handleCreateTheme}
              className={cn(
                'flex items-center gap-2 px-3 py-2 rounded-lg',
                'bg-green-500 text-white',
                'hover:bg-green-600 transition-colors',
                'focus:outline-none focus:ring-2 focus:ring-green-500/20'
              )}
            >
              <Plus className="w-4 h-4" />
              <span className="text-sm font-medium">Add Theme</span>
            </button>
          )}
        </div>
      </div>

      {/* Modern Filter Panel */}
      <div className="bg-gradient-to-r from-purple-50/50 via-blue-50/50 to-indigo-50/50 dark:from-purple-950/20 dark:via-blue-950/20 dark:to-indigo-950/20 rounded-xl border border-purple-200/50 dark:border-purple-800/30 p-6 backdrop-blur-sm">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg">
            <Filter className="w-4 h-4 text-white" />
          </div>
          <h3 className="text-lg font-semibold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
            Discover Themes
          </h3>
          <Sparkles className="w-4 h-4 text-purple-500 animate-pulse" />
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Category Filter */}
          <div className="space-y-3">
            <label className="flex items-center gap-2 text-sm font-medium text-foreground">
              <Palette className="w-4 h-4 text-purple-500" />
              Category
            </label>
            <div className="relative">
              <select
                value={selectedCategory}
                onChange={(e) => handleCategoryFilter(e.target.value)}
                className={cn(
                  'w-full px-4 py-3 rounded-xl border-2 border-purple-200/50 dark:border-purple-800/30',
                  'bg-white/80 dark:bg-gray-900/80 text-foreground text-sm backdrop-blur-sm',
                  'focus:outline-none focus:ring-2 focus:ring-purple-500/30 focus:border-purple-500/50',
                  'hover:border-purple-300/70 dark:hover:border-purple-700/50',
                  'transition-all duration-300 ease-out',
                  'appearance-none cursor-pointer',
                  'shadow-sm hover:shadow-md'
                )}
              >
                <option value="">✨ All Categories</option>
                {categories.map((category: string) => (
                  <option key={category} value={category}>
                    🎨 {category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-purple-400 pointer-events-none" />
            </div>
          </div>

          {/* Status Filter */}
          <div className="space-y-3">
            <label className="flex items-center gap-2 text-sm font-medium text-foreground">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              Status
            </label>
            <div className="relative">
              <select
                value={filters.is_active === undefined ? 'all' : filters.is_active ? 'active' : 'inactive'}
                onChange={(e) => {
                  const value = e.target.value
                  if (value === 'all') {
                    setFilters({ is_active: undefined })
                  } else {
                    setFilters({ is_active: value === 'active' })
                  }
                }}
                className={cn(
                  'w-full px-4 py-3 rounded-xl border-2 border-blue-200/50 dark:border-blue-800/30',
                  'bg-white/80 dark:bg-gray-900/80 text-foreground text-sm backdrop-blur-sm',
                  'focus:outline-none focus:ring-2 focus:ring-blue-500/30 focus:border-blue-500/50',
                  'hover:border-blue-300/70 dark:hover:border-blue-700/50',
                  'transition-all duration-300 ease-out',
                  'appearance-none cursor-pointer',
                  'shadow-sm hover:shadow-md'
                )}
              >
                <option value="all">🌟 All Themes</option>
                <option value="active">✅ Active Only</option>
                <option value="inactive">⏸️ Inactive Only</option>
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-blue-400 pointer-events-none" />
            </div>
          </div>

          {/* Sort Order Filter */}
          <div className="space-y-3">
            <label className="flex items-center gap-2 text-sm font-medium text-foreground">
              <ArrowUpDown className="w-4 h-4 text-indigo-500" />
              Sort Order
            </label>
            <div className="relative">
              <select
                value={selectedSortOrder}
                onChange={(e) => handleSortOrderChange(e.target.value as 'asc' | 'desc')}
                className={cn(
                  'w-full px-4 py-3 rounded-xl border-2 border-indigo-200/50 dark:border-indigo-800/30',
                  'bg-white/80 dark:bg-gray-900/80 text-foreground text-sm backdrop-blur-sm',
                  'focus:outline-none focus:ring-2 focus:ring-indigo-500/30 focus:border-indigo-500/50',
                  'hover:border-indigo-300/70 dark:hover:border-indigo-700/50',
                  'transition-all duration-300 ease-out',
                  'appearance-none cursor-pointer',
                  'shadow-sm hover:shadow-md'
                )}
              >
                <option value="asc">📈 Ascending (A-Z)</option>
                <option value="desc">📉 Descending (Z-A)</option>
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-indigo-400 pointer-events-none" />
            </div>
          </div>
        </div>

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="mt-6 pt-4 border-t border-purple-200/30 dark:border-purple-800/30">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-purple-600 dark:text-purple-400">Active Filters:</span>
                <div className="flex gap-2">
                  {filters.search && (
                    <span className="px-3 py-1 bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 rounded-full text-xs font-medium">
                      Search: "{filters.search}"
                    </span>
                  )}
                  {filters.category && (
                    <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded-full text-xs font-medium">
                      Category: {filters.category}
                    </span>
                  )}
                  {filters.sort_order && filters.sort_order !== 'asc' && (
                    <span className="px-3 py-1 bg-indigo-100 dark:bg-indigo-900/50 text-indigo-700 dark:text-indigo-300 rounded-full text-xs font-medium">
                      Sort: {filters.sort_order === 'desc' ? 'Descending' : 'Ascending'}
                    </span>
                  )}
                </div>
              </div>
              <button
                onClick={handleClearFilters}
                className="flex items-center gap-1 px-3 py-1 text-xs font-medium text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors"
              >
                <X className="w-3 h-3" />
                Clear All
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )

  // Bottom content (pagination)
  const bottomContent = !loading && !error && themes.length > 0 ? (
    <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
      {/* Page Size Selector */}
      <div className="flex items-center gap-2">
        <span className="text-sm text-muted-foreground">Show:</span>
        <select
          value={filters.limit}
          onChange={(e) => setFilters({ limit: parseInt(e.target.value) })}
          className="px-3 py-1.5 text-sm border border-border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
        >
          <option value={12}>12 per page</option>
          <option value={24}>24 per page</option>
          <option value={36}>36 per page</option>
          <option value={48}>48 per page</option>
        </select>
      </div>

      {/* Pagination Controls */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center gap-2">
          <button
            onClick={() => setFilters({ page: filters.page - 1 })}
            disabled={filters.page <= 1}
            className="px-3 py-2 text-sm border border-border rounded-lg hover:bg-accent disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>

          <span className="px-4 py-2 text-sm text-muted-foreground">
            Page {filters.page} of {pagination.totalPages}
          </span>

          <button
            onClick={() => setFilters({ page: filters.page + 1 })}
            disabled={filters.page >= pagination.totalPages}
            className="px-3 py-2 text-sm border border-border rounded-lg hover:bg-accent disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
      )}

      {/* Results Info */}
      <div className="text-sm text-muted-foreground">
        Showing {((filters.page - 1) * filters.limit) + 1} to {Math.min(filters.page * filters.limit, pagination.totalItems)} of {pagination.totalItems} results
      </div>
    </div>
  ) : null

  return (
    <MainLayout
      title="🎨 Themes"
      description="Explore themes and discover content collections"
      topContent={topContent}
      bottomContent={bottomContent}
    >
      {/* Themes grid/list */}
      <div className="p-4 lg:p-6">
      {loading ? (
        <LoadingGrid
          count={8}
          type="theme"
          className={cn(
            viewMode === 'grid'
              ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              : 'grid-cols-1'
          )}
        />
      ) : error ? (
        <EmptyState
          type="error"
          title="Failed to load themes"
          description={error}
          actionLabel="Try again"
          onAction={refetch}
        />
      ) : themes.length === 0 ? (
        <EmptyState
          type={hasActiveFilters ? 'search' : 'themes'}
          actionLabel={hasActiveFilters ? 'Clear filters' : 'Refresh'}
          onAction={hasActiveFilters ? handleClearFilters : refetch}
        />
      ) : (
        <div className={cn(
          'grid gap-6',
          viewMode === 'grid'
            ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
            : 'grid-cols-1 max-w-4xl mx-auto'
        )}>
          {themes.map((theme: any) => (
            <ThemeCard
              key={theme.id}
              theme={theme}
              onClick={handleThemeClick}
              onEdit={isAdmin ? handleEditTheme : undefined}
              onDelete={isAdmin ? handleDeleteTheme : undefined}
              onViewTasks={handleViewTasks}
              showActions={isAdmin}
              showStats={true}
              className={cn(
                viewMode === 'list' && 'flex-row items-center p-4'
              )}
            />
          ))}
        </div>
      )}
      </div>

      {/* Theme Management Modal */}
      <ThemeModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={handleSaveTheme}
        theme={editingTheme}
        loading={modalLoading}
      />
    </MainLayout>
  )
}

export default Atelier
