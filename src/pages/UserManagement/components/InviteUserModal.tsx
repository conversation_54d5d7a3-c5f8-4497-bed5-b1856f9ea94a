import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, UserPlus, Co<PERSON>, Check, AlertCircle, ExternalLink } from 'lucide-react'
import userManagementService, { InviteUserRequest } from '../../../services/userManagement/userManagementService'
import { cn } from '../../../utils/cn'

interface InviteUserModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

/**
 * Invite User Modal - Create invitation tokens for new users
 */
const InviteUserModal: React.FC<InviteUserModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const [formData, setFormData] = useState<InviteUserRequest>({
    username: '',
    role: 'agent'
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [invitationToken, setInvitationToken] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)
  const [copiedUrl, setCopiedUrl] = useState(false)
  const [countdown, setCountdown] = useState(5)
  const [autoRedirect, setAutoRedirect] = useState(false)

  // Auto-redirect countdown effect
  useEffect(() => {
    if (invitationToken && autoRedirect && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(prev => prev - 1)
      }, 1000)
      return () => clearTimeout(timer)
    } else if (invitationToken && autoRedirect && countdown === 0) {
      navigateToRegister()
    }
  }, [invitationToken, autoRedirect, countdown])

  // Navigate to registration page with token
  const navigateToRegister = () => {
    if (invitationToken) {
      const registerUrl = `${window.location.origin}/register?token=${invitationToken}`
      window.open(registerUrl, '_blank')
    }
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await userManagementService.inviteUser(formData)
      
      if (response.success) {
        setInvitationToken(response.registration_token)
        setSuccess(response.msg)
        setAutoRedirect(true) // Start auto-redirect countdown
        onSuccess?.()
      } else {
        setError(response.msg || 'Failed to create invitation')
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create invitation')
    } finally {
      setLoading(false)
    }
  }

  // Copy token to clipboard
  const copyToken = async () => {
    if (invitationToken) {
      try {
        await navigator.clipboard.writeText(invitationToken)
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      } catch (err) {
        console.error('Failed to copy token:', err)
      }
    }
  }

  // Copy encoded registration URL to clipboard
  const copyRegistrationUrl = async () => {
    if (invitationToken) {
      try {
        // Create encoded URL that's safe for messaging apps
        const baseUrl = `${window.location.origin}/register`
        const encodedToken = encodeURIComponent(invitationToken)
        const fullUrl = `${baseUrl}?token=${encodedToken}`

        await navigator.clipboard.writeText(fullUrl)
        setCopiedUrl(true)
        setTimeout(() => setCopiedUrl(false), 2000)
      } catch (err) {
        console.error('Failed to copy URL:', err)
      }
    }
  }

  // Reset modal state
  const resetModal = () => {
    setFormData({ username: '', role: 'agent' })
    setError(null)
    setSuccess(null)
    setInvitationToken(null)
    setCopied(false)
    setCopiedUrl(false)
    setCountdown(5)
    setAutoRedirect(false)
  }

  // Handle close
  const handleClose = () => {
    resetModal()
    onClose()
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleClose}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative w-full max-w-md bg-card border border-border rounded-2xl shadow-2xl overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-border">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <UserPlus className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-foreground">Invite User</h2>
                  <p className="text-sm text-muted-foreground">Create an invitation for a new user</p>
                </div>
              </div>
              <button
                onClick={handleClose}
                className="p-2 hover:bg-muted rounded-lg transition-colors"
              >
                <X className="h-4 w-4" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6">
              {!invitationToken ? (
                /* Invitation Form */
                <form onSubmit={handleSubmit} className="space-y-4">
                  {/* Username */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Username
                    </label>
                    <input
                      type="text"
                      value={formData.username}
                      onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                      placeholder="Enter username"
                      required
                      className="w-full px-3 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                    />
                  </div>

                  {/* Role */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Role
                    </label>
                    <select
                      value={formData.role}
                      onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value as any }))}
                      className="w-full px-3 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                    >
                      <option value="agent">Agent</option>
                      <option value="supervisor">Supervisor</option>
                      <option value="admin">Admin</option>
                    </select>
                  </div>

                  {/* Error Message */}
                  {error && (
                    <div className="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
                      <AlertCircle className="h-4 w-4 text-destructive flex-shrink-0" />
                      <p className="text-sm text-destructive">{error}</p>
                    </div>
                  )}

                  {/* Submit Button */}
                  <div className="flex gap-3 pt-2">
                    <button
                      type="button"
                      onClick={handleClose}
                      className="flex-1 px-4 py-2 border border-border rounded-lg hover:bg-muted transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading || !formData.username.trim()}
                      className="flex-1 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? 'Creating...' : 'Create Invitation'}
                    </button>
                  </div>
                </form>
              ) : (
                /* Success State with Token */
                <div className="space-y-4">
                  {/* Success Message */}
                  <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                    <Check className="h-4 w-4 text-green-600 dark:text-green-400 flex-shrink-0" />
                    <p className="text-sm text-green-700 dark:text-green-300">{success}</p>
                  </div>

                  {/* Token Display */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Invitation Token
                    </label>
                    <div className="relative">
                      <textarea
                        value={invitationToken}
                        readOnly
                        rows={4}
                        className="w-full px-3 py-2 bg-muted border border-border rounded-lg text-sm font-mono resize-none"
                      />
                      <button
                        onClick={copyToken}
                        className={cn(
                          "absolute top-2 right-2 p-2 rounded-lg transition-colors",
                          copied
                            ? "bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400"
                            : "bg-background hover:bg-muted border border-border"
                        )}
                      >
                        {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  {/* Registration URL */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Registration URL (Safe for messaging apps)
                    </label>
                    <div className="relative">
                      <input
                        value={`${window.location.origin}/register?token=${encodeURIComponent(invitationToken || '')}`}
                        readOnly
                        className="w-full px-3 py-2 pr-12 bg-muted border border-border rounded-lg text-sm truncate"
                      />
                      <button
                        onClick={copyRegistrationUrl}
                        className={cn(
                          "absolute top-1/2 right-2 -translate-y-1/2 p-2 rounded-lg transition-colors",
                          copiedUrl
                            ? "bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400"
                            : "bg-background hover:bg-muted border border-border"
                        )}
                        title="Copy registration URL"
                      >
                        {copiedUrl ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </button>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      This URL is encoded and safe to share via Viber, Messenger, WhatsApp, etc.
                    </p>
                  </div>

                  {/* Auto-redirect notification */}
                  {autoRedirect && countdown > 0 && (
                    <div className="p-4 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-amber-500 rounded-full animate-pulse" />
                          <p className="text-sm font-medium text-amber-700 dark:text-amber-300">
                            Auto-opening registration page in {countdown}s
                          </p>
                        </div>
                        <button
                          onClick={() => setAutoRedirect(false)}
                          className="text-xs text-amber-600 dark:text-amber-400 hover:text-amber-800 dark:hover:text-amber-200 font-medium"
                        >
                          Cancel
                        </button>
                      </div>
                      <div className="mt-2 w-full bg-amber-200 dark:bg-amber-800 rounded-full h-1">
                        <div
                          className="bg-amber-500 h-1 rounded-full transition-all duration-1000 ease-linear"
                          style={{ width: `${((5 - countdown) / 5) * 100}%` }}
                        />
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="grid grid-cols-2 gap-3">
                    <button
                      onClick={navigateToRegister}
                      className="inline-flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-200 font-medium shadow-lg hover:shadow-xl"
                    >
                      <ExternalLink className="h-4 w-4" />
                      Open Now
                    </button>
                    <button
                      onClick={copyRegistrationUrl}
                      className={cn(
                        "inline-flex items-center justify-center gap-2 px-4 py-3 rounded-lg transition-all duration-200 font-medium shadow-lg hover:shadow-xl",
                        copiedUrl
                          ? "bg-gradient-to-r from-green-600 to-emerald-600 text-white"
                          : "bg-gradient-to-r from-blue-600 to-cyan-600 text-white hover:from-blue-700 hover:to-cyan-700"
                      )}
                    >
                      {copiedUrl ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      {copiedUrl ? 'Copied!' : 'Copy URL'}
                    </button>
                  </div>

                  {/* Close Button */}
                  <button
                    onClick={handleClose}
                    className="w-full px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors font-medium"
                  >
                    Close
                  </button>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  )
}

export default InviteUserModal
